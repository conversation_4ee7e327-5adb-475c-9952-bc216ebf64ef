using Discord.WebSocket;

namespace ZeroDateStrat.Services;

/// <summary>
/// Handler for ChatGPT bot functionality in Discord
/// </summary>
public interface IChatGPTBotHandler
{
    /// <summary>
    /// Process a Discord message to check if it should trigger ChatGPT response
    /// </summary>
    /// <param name="message">Discord message to process</param>
    /// <returns>True if message was handled</returns>
    Task<bool> HandleMessageAsync(SocketMessage message);

    /// <summary>
    /// Extract prompt from a Discord message that mentions the bot or uses trigger keywords
    /// </summary>
    /// <param name="messageContent">The full message content</param>
    /// <param name="botUserId">The bot's user ID for mention detection</param>
    /// <returns>Extracted prompt or null if no valid prompt found</returns>
    string? ExtractPrompt(string messageContent, ulong? botUserId = null);

    /// <summary>
    /// Check if a message should trigger ChatGPT response
    /// </summary>
    /// <param name="message">Discord message to check</param>
    /// <returns>True if message should trigger ChatGPT</returns>
    bool ShouldTriggerChatGPT(SocketMessage message);

    /// <summary>
    /// Format the ChatGPT response for Discord
    /// </summary>
    /// <param name="response">Raw response from OpenAI</param>
    /// <param name="maxLength">Maximum message length for Discord</param>
    /// <returns>Formatted response ready for Discord</returns>
    string FormatResponse(string response, int maxLength = 2000);
}
