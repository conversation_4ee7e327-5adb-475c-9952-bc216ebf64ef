using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using ZeroDateStrat.Services;

namespace ZeroDateStrat.Tests;

/// <summary>
/// Test for ChatGPT bot functionality
/// </summary>
public class ChatGPTBotTest
{
    public static async Task RunAsync()
    {
        Console.WriteLine("🤖 Starting ChatGPT Bot Test...\n");
        Console.WriteLine("Test is running...");

        try
        {
            Console.WriteLine("✅ Basic test functionality working");

            // Test 1: Test prompt extraction without DI
            Console.WriteLine("\n🔍 Test 1: Testing prompt extraction...");

            var testMessages = new[]
            {
                "@ChatGptBot How can I implement a VIX scraper in C#?",
                "!askchatgpt What is the best way to handle options data?",
                "!chatgpt Explain the Black-Scholes model"
            };

            foreach (var testMessage in testMessages)
            {
                // Simple extraction logic for testing
                var extractedPrompt = ExtractPromptSimple(testMessage);
                Console.WriteLine($"   Input: {testMessage}");
                Console.WriteLine($"   Extracted: {extractedPrompt ?? "null"}");
                Console.WriteLine();
            }

            Console.WriteLine("🎉 ChatGPT Bot Test completed successfully!");
            Console.WriteLine("Note: Full integration test requires OpenAI API key configuration.");
            Console.WriteLine("To test with OpenAI API, set OPENAI_API_KEY environment variable.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Test failed with error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    private static string? ExtractPromptSimple(string messageContent)
    {
        if (string.IsNullOrWhiteSpace(messageContent))
        {
            return null;
        }

        var content = messageContent.Trim();

        // Handle mention triggers
        content = content.Replace("@ChatGptBot", "").Replace("@ChatGPT", "").Trim();

        // Handle keyword triggers
        var keywords = new[] { "!askchatgpt", "!chatgpt", "!gpt" };
        foreach (var keyword in keywords)
        {
            if (content.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
            {
                content = content.Substring(keyword.Length).Trim();
                break;
            }
        }

        return string.IsNullOrWhiteSpace(content) ? null : content;
    }
}


